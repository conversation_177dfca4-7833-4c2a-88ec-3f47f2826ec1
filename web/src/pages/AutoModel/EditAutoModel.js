import React, { useEffect, useState } from 'react';
import {
  API,
  showError,
  showSuccess,
} from '../../helpers';
import {
  Button,
  SideSheet,
  Space,
  Spin,
  Typography,
  Form,
  Input,
  Switch,
  TagInput,
  Divider,
} from '@douyinfe/semi-ui';
import {
  IconSave,
  IconClose,
} from '@douyinfe/semi-icons';
import { useTranslation } from 'react-i18next';

const { Text, Title } = Typography;

const EditAutoModel = ({ refresh, editingAutoModel, visiable, handleClose }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [formApi, setFormApi] = useState(null);

  const isEdit = editingAutoModel.id !== undefined;

  const initFormValues = {
    name: '',
    description: '',
    is_active: true,
    models: [],
  };

  useEffect(() => {
    if (visiable && isEdit) {
      setLoading(true);
      // 如果是编辑模式，设置表单值
      if (formApi) {
        formApi.setValues({
          name: editingAutoModel.name || '',
          description: editingAutoModel.description || '',
          is_active: editingAutoModel.is_active !== false,
          models: editingAutoModel.models || [],
        });
      }
      setLoading(false);
    } else if (visiable && !isEdit) {
      // 如果是新建模式，重置表单
      if (formApi) {
        formApi.setValues(initFormValues);
      }
    }
  }, [visiable, isEdit, editingAutoModel, formApi]);

  const submit = async (values) => {
    setLoading(true);
    try {
      let res;
      const payload = {
        name: values.name,
        description: values.description || '',
        is_active: values.is_active,
        models: values.models || [],
      };

      if (isEdit) {
        // 更新现有配置
        res = await API.put(`/api/auto_models/${editingAutoModel.name}`, payload);
      } else {
        // 创建新配置
        res = await API.post('/api/auto_models', payload);
      }

      const { success, message } = res.data;
      if (success) {
        showSuccess(isEdit ? t('Auto Model 更新成功！') : t('Auto Model 创建成功！'));
        handleClose();
        refresh();
      } else {
        showError(message);
      }
    } catch (error) {
      showError(error.message);
    }
    setLoading(false);
  };

  return (
    <SideSheet
      title={
        <div className="flex items-center">
          <Title heading={6} style={{ margin: 0 }}>
            {isEdit ? t('编辑 Auto Model') : t('创建 Auto Model')}
          </Title>
        </div>
      }
      visible={visiable}
      onCancel={handleClose}
      width={600}
      bodyStyle={{ padding: 0 }}
      headerStyle={{ padding: '20px 24px 0' }}
      closeIcon={<IconClose />}
    >
      <Spin spinning={loading}>
        <div className="p-6">
          <Form
            initValues={initFormValues}
            getFormApi={(api) => setFormApi(api)}
            onSubmit={submit}
            labelPosition="top"
            style={{ width: '100%' }}
          >
            <Form.Input
              field="name"
              label={t('名称')}
              placeholder={t('请输入 Auto Model 名称')}
              rules={[
                { required: true, message: t('请输入名称') },
                { 
                  pattern: /^[a-zA-Z0-9_-]+$/, 
                  message: t('名称只能包含字母、数字、下划线和连字符') 
                }
              ]}
              disabled={isEdit} // 编辑时不允许修改名称
            />

            <Form.Input
              field="description"
              label={t('描述')}
              placeholder={t('请输入描述信息（可选）')}
            />

            <Form.Switch
              field="is_active"
              label={t('启用状态')}
              checkedText={t('启用')}
              uncheckedText={t('禁用')}
            />

            <Form.TagInput
              field="models"
              label={t('模型列表')}
              placeholder={t('请输入模型名称，按回车添加')}
              rules={[
                { required: true, message: t('请至少添加一个模型') },
                {
                  validator: (rule, value) => {
                    if (!value || value.length === 0) {
                      return Promise.reject(t('请至少添加一个模型'));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
              allowDuplicates={false}
              showClear
              style={{ width: '100%' }}
            />

            <Divider />

            <div className="flex justify-end gap-3">
              <Button onClick={handleClose}>
                {t('取消')}
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<IconSave />}
              >
                {isEdit ? t('更新') : t('创建')}
              </Button>
            </div>
          </Form>
        </div>
      </Spin>
    </SideSheet>
  );
};

export default EditAutoModel;
